<template>
  <div class="user-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>👥 用户管理系统</h1>
      <p>管理系统用户信息，支持增删改查操作</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="search-box">
        <input
          v-model="searchKeyword"
          type="text"
          placeholder="搜索用户名、邮箱或手机号..."
          class="search-input"
          @input="handleSearch"
        />
        <button class="search-btn" @click="handleSearch">🔍 搜索</button>
      </div>
      <div class="action-buttons">
        <button class="btn btn-primary" @click="showAddDialog">➕ 新增用户</button>
        <button class="btn btn-success" @click="exportUsers">📊 导出数据</button>
        <button class="btn btn-warning" @click="refreshData">🔄 刷新</button>
      </div>
    </div>

    <!-- 用户统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon">👥</div>
        <div class="stat-info">
          <h3>{{ totalUsers }}</h3>
          <p>总用户数</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-info">
          <h3>{{ activeUsers }}</h3>
          <p>活跃用户</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">🚫</div>
        <div class="stat-info">
          <h3>{{ inactiveUsers }}</h3>
          <p>禁用用户</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">🆕</div>
        <div class="stat-info">
          <h3>{{ newUsersToday }}</h3>
          <p>今日新增</p>
        </div>
      </div>
    </div>

    <!-- 用户列表表格 -->
    <div class="table-container">
      <table class="user-table">
        <thead>
          <tr>
            <th>
              <input type="checkbox" v-model="selectAll" @change="handleSelectAll" />
            </th>
            <th>头像</th>
            <th>用户名</th>
            <th>邮箱</th>
            <th>手机号</th>
            <th>角色</th>
            <th>状态</th>
            <th>注册时间</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="user in filteredUsers" :key="user.id" :class="{ 'selected': selectedUsers.includes(user.id) }">
            <td>
              <input type="checkbox" :value="user.id" v-model="selectedUsers" />
            </td>
            <td>
              <div class="avatar">{{ user.avatar || '👤' }}</div>
            </td>
            <td>
              <div class="user-name">
                <strong>{{ user.username }}</strong>
                <small>ID: {{ user.id }}</small>
              </div>
            </td>
            <td>{{ user.email }}</td>
            <td>{{ user.phone }}</td>
            <td>
              <span class="role-tag" :class="user.role">{{ getRoleText(user.role) }}</span>
            </td>
            <td>
              <span class="status-tag" :class="user.status">
                {{ user.status === 'active' ? '✅ 活跃' : '🚫 禁用' }}
              </span>
            </td>
            <td>{{ formatDate(user.createdAt) }}</td>
            <td>
              <div class="action-buttons">
                <button class="btn-small btn-info" @click="viewUser(user)" title="查看详情">👁️</button>
                <button class="btn-small btn-primary" @click="editUser(user)" title="编辑">✏️</button>
                <button class="btn-small btn-danger" @click="deleteUser(user)" title="删除">🗑️</button>
                <button
                  class="btn-small"
                  :class="user.status === 'active' ? 'btn-warning' : 'btn-success'"
                  @click="toggleUserStatus(user)"
                  :title="user.status === 'active' ? '禁用' : '启用'"
                >
                  {{ user.status === 'active' ? '🚫' : '✅' }}
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <button class="btn" :disabled="currentPage === 1" @click="changePage(currentPage - 1)">上一页</button>
      <span class="page-info">第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>
      <button class="btn" :disabled="currentPage === totalPages" @click="changePage(currentPage + 1)">下一页</button>
    </div>

    <!-- 新增/编辑用户对话框 -->
    <div v-if="showDialog" class="dialog-overlay" @click="closeDialog">
      <div class="dialog" @click.stop>
        <div class="dialog-header">
          <h3>{{ isEditing ? '编辑用户' : '新增用户' }}</h3>
          <button class="close-btn" @click="closeDialog">✕</button>
        </div>
        <div class="dialog-body">
          <form @submit.prevent="saveUser">
            <div class="form-group">
              <label>用户名 *</label>
              <input v-model="currentUser.username" type="text" required />
            </div>
            <div class="form-group">
              <label>邮箱 *</label>
              <input v-model="currentUser.email" type="email" required />
            </div>
            <div class="form-group">
              <label>手机号</label>
              <input v-model="currentUser.phone" type="tel" />
            </div>
            <div class="form-group">
              <label>角色</label>
              <select v-model="currentUser.role">
                <option value="admin">管理员</option>
                <option value="user">普通用户</option>
                <option value="guest">访客</option>
              </select>
            </div>
            <div class="form-group">
              <label>状态</label>
              <select v-model="currentUser.status">
                <option value="active">活跃</option>
                <option value="inactive">禁用</option>
              </select>
            </div>
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" @click="closeDialog">取消</button>
              <button type="submit" class="btn btn-primary">{{ isEditing ? '更新' : '创建' }}</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="selectedUsers.length > 0" class="batch-actions">
      <span>已选择 {{ selectedUsers.length }} 个用户</span>
      <button class="btn btn-warning" @click="batchToggleStatus">批量切换状态</button>
      <button class="btn btn-danger" @click="batchDelete">批量删除</button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const searchKeyword = ref('')
const selectedUsers = ref([])
const selectAll = ref(false)
const showDialog = ref(false)
const isEditing = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)

// 当前编辑的用户
const currentUser = ref({
  id: null,
  username: '',
  email: '',
  phone: '',
  role: 'user',
  status: 'active',
  avatar: '👤',
  createdAt: new Date()
})

// 模拟用户数据
const users = ref([
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    phone: '13800138000',
    role: 'admin',
    status: 'active',
    avatar: '👨‍💼',
    createdAt: new Date('2024-01-01')
  },
  {
    id: 2,
    username: 'zhangsan',
    email: '<EMAIL>',
    phone: '13800138001',
    role: 'user',
    status: 'active',
    avatar: '👨',
    createdAt: new Date('2024-01-15')
  },
  {
    id: 3,
    username: 'lisi',
    email: '<EMAIL>',
    phone: '13800138002',
    role: 'user',
    status: 'inactive',
    avatar: '👩',
    createdAt: new Date('2024-02-01')
  },
  {
    id: 4,
    username: 'wangwu',
    email: '<EMAIL>',
    phone: '13800138003',
    role: 'guest',
    status: 'active',
    avatar: '👦',
    createdAt: new Date('2024-02-15')
  },
  {
    id: 5,
    username: 'zhaoliu',
    email: '<EMAIL>',
    phone: '13800138004',
    role: 'user',
    status: 'active',
    avatar: '👧',
    createdAt: new Date('2024-03-01')
  }
])

// 计算属性
const filteredUsers = computed(() => {
  if (!searchKeyword.value) return users.value

  return users.value.filter(user =>
    user.username.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    user.email.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    user.phone.includes(searchKeyword.value)
  )
})

const totalUsers = computed(() => users.value.length)
const activeUsers = computed(() => users.value.filter(u => u.status === 'active').length)
const inactiveUsers = computed(() => users.value.filter(u => u.status === 'inactive').length)
const newUsersToday = computed(() => {
  const today = new Date().toDateString()
  return users.value.filter(u => new Date(u.createdAt).toDateString() === today).length
})

const totalPages = computed(() => Math.ceil(filteredUsers.value.length / pageSize.value))

// 方法
const getRoleText = (role) => {
  const roleMap = {
    admin: '管理员',
    user: '普通用户',
    guest: '访客'
  }
  return roleMap[role] || role
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleSelectAll = () => {
  if (selectAll.value) {
    selectedUsers.value = filteredUsers.value.map(u => u.id)
  } else {
    selectedUsers.value = []
  }
}

const showAddDialog = () => {
  isEditing.value = false
  currentUser.value = {
    id: null,
    username: '',
    email: '',
    phone: '',
    role: 'user',
    status: 'active',
    avatar: '👤',
    createdAt: new Date()
  }
  showDialog.value = true
}

const editUser = (user) => {
  isEditing.value = true
  currentUser.value = { ...user }
  showDialog.value = true
}

const closeDialog = () => {
  showDialog.value = false
}

const saveUser = () => {
  if (isEditing.value) {
    // 更新用户
    const index = users.value.findIndex(u => u.id === currentUser.value.id)
    if (index !== -1) {
      users.value[index] = { ...currentUser.value }
      alert('✅ 用户更新成功！')
    }
  } else {
    // 新增用户
    const newId = Math.max(...users.value.map(u => u.id)) + 1
    users.value.push({
      ...currentUser.value,
      id: newId,
      createdAt: new Date()
    })
    alert('✅ 用户创建成功！')
  }
  closeDialog()
}

const deleteUser = (user) => {
  if (confirm(`确定要删除用户 "${user.username}" 吗？`)) {
    const index = users.value.findIndex(u => u.id === user.id)
    if (index !== -1) {
      users.value.splice(index, 1)
      alert('✅ 用户删除成功！')
    }
  }
}

const toggleUserStatus = (user) => {
  user.status = user.status === 'active' ? 'inactive' : 'active'
  alert(`✅ 用户状态已${user.status === 'active' ? '启用' : '禁用'}！`)
}

const viewUser = (user) => {
  alert(`👁️ 查看用户详情：\n\n用户名：${user.username}\n邮箱：${user.email}\n手机：${user.phone}\n角色：${getRoleText(user.role)}\n状态：${user.status === 'active' ? '活跃' : '禁用'}\n注册时间：${formatDate(user.createdAt)}`)
}

const changePage = (page) => {
  currentPage.value = page
}

const exportUsers = () => {
  alert('📊 导出功能开发中...')
}

const refreshData = () => {
  alert('🔄 数据已刷新！')
}

const batchToggleStatus = () => {
  selectedUsers.value.forEach(userId => {
    const user = users.value.find(u => u.id === userId)
    if (user) {
      user.status = user.status === 'active' ? 'inactive' : 'active'
    }
  })
  selectedUsers.value = []
  selectAll.value = false
  alert('✅ 批量状态切换成功！')
}

const batchDelete = () => {
  if (confirm(`确定要删除选中的 ${selectedUsers.value.length} 个用户吗？`)) {
    users.value = users.value.filter(u => !selectedUsers.value.includes(u.id))
    selectedUsers.value = []
    selectAll.value = false
    alert('✅ 批量删除成功！')
  }
}

// 生命周期
onMounted(() => {
  console.log('👥 用户管理系统已加载')
})
</script>

<style scoped>
/* 页面整体样式 */
.user-management {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  margin: 0 0 10px 0;
  font-size: 2.5em;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 1.1em;
}

/* 工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex-wrap: wrap;
  gap: 15px;
}

.search-box {
  display: flex;
  gap: 10px;
  flex: 1;
  max-width: 400px;
}

.search-input {
  flex: 1;
  padding: 10px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.search-input:focus {
  outline: none;
  border-color: #409eff;
}

.search-btn {
  padding: 10px 20px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.3s;
}

.search-btn:hover {
  background: #337ecc;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

/* 按钮样式 */
.btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background: #409eff;
  color: white;
}

.btn-primary:hover {
  background: #337ecc;
}

.btn-success {
  background: #67c23a;
  color: white;
}

.btn-success:hover {
  background: #5daf34;
}

.btn-warning {
  background: #e6a23c;
  color: white;
}

.btn-warning:hover {
  background: #cf9236;
}

.btn-danger {
  background: #f56c6c;
  color: white;
}

.btn-danger:hover {
  background: #f45454;
}

.btn-info {
  background: #909399;
  color: white;
}

.btn-info:hover {
  background: #82848a;
}

.btn-secondary {
  background: #dcdfe6;
  color: #606266;
}

.btn-secondary:hover {
  background: #c8c9cc;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn-small {
  padding: 5px 8px;
  font-size: 12px;
  min-width: 30px;
}

/* 统计卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 15px;
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 2.5em;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  color: white;
}

.stat-info h3 {
  margin: 0;
  font-size: 2em;
  font-weight: 600;
  color: #303133;
}

.stat-info p {
  margin: 5px 0 0 0;
  color: #909399;
  font-size: 14px;
}

/* 表格样式 */
.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.user-table {
  width: 100%;
  border-collapse: collapse;
}

.user-table th,
.user-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #ebeef5;
}

.user-table th {
  background: #f5f7fa;
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.user-table tr:hover {
  background: #f5f7fa;
}

.user-table tr.selected {
  background: #ecf5ff;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 18px;
}

.user-name strong {
  display: block;
  color: #303133;
  margin-bottom: 2px;
}

.user-name small {
  color: #909399;
  font-size: 12px;
}

.role-tag,
.status-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.role-tag.admin {
  background: #f4f4f5;
  color: #909399;
}

.role-tag.user {
  background: #e1f3d8;
  color: #67c23a;
}

.role-tag.guest {
  background: #fdf6ec;
  color: #e6a23c;
}

.status-tag.active {
  background: #e1f3d8;
  color: #67c23a;
}

.status-tag.inactive {
  background: #fef0f0;
  color: #f56c6c;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-info {
  color: #606266;
  font-size: 14px;
}

/* 对话框 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
}

.dialog-header h3 {
  margin: 0;
  color: #303133;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #909399;
  padding: 5px;
  border-radius: 4px;
}

.close-btn:hover {
  background: #f5f7fa;
}

.dialog-body {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: #303133;
  font-weight: 500;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #409eff;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 30px;
}

/* 批量操作栏 */
.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 15px;
  z-index: 100;
}

.batch-actions span {
  color: #606266;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-management {
    padding: 10px;
  }

  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    max-width: none;
  }

  .stats-cards {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .user-table {
    font-size: 12px;
  }

  .user-table th,
  .user-table td {
    padding: 8px 10px;
  }

  .dialog {
    width: 95%;
    margin: 10px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-management > * {
  animation: fadeIn 0.5s ease-out;
}
</style>
